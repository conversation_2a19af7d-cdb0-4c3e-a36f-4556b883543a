from __future__ import annotations
import json, logging, os, re, time
from contextlib import contextmanager
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple
from datetime import datetime, timezone, timedelta
from statistics import mean
from db import session_scope, init_db
from celery_app import app
from models import FsMetricSample, FsMetricAgg5m, FsMetricAgg60m, OnuStatusCurrent, OnuOutage

from config import settings

log = logging.getLogger(__name__)

# ----- Paths / state ---------------------------------------------------------
STATE_DIR = Path(getattr(settings, "STATE_DIR", "/var/lib/outage-monitor")).resolve()
RUN_DIR   = Path(os.getenv("RUN_DIR", "/run/outage-monitor")).resolve()
RUN_DIR.mkdir(parents=True, exist_ok=True)
STATE_DIR.mkdir(parents=True, exist_ok=True)

OFFLINE_STATE_PATH = STATE_DIR / "offline_state.json"  # ONU offline first-seen times

def _load_json(path: Path) -> dict:
    try:
        with open(path, "r", encoding="utf-8") as f:
            return json.load(f)
    except Exception:
        return {}

def _atomic_write_json(path: Path, data: dict) -> None:
    tmp = path.with_suffix(".tmp")
    with open(tmp, "w", encoding="utf-8") as f:
        json.dump(data, f, indent=2, sort_keys=True)
    os.replace(tmp, path)

# ----- Overlap guard (single-flight) ----------------------------------------
@contextmanager
def non_overlapping(name: str):
    import fcntl
    lock_path = RUN_DIR / f"{name}.lock"
    with open(lock_path, "w") as fh:
        try:
            fcntl.flock(fh.fileno(), fcntl.LOCK_EX | fcntl.LOCK_NB)
            yield
        except BlockingIOError:
            log.warning("Skip %s: previous run still active", name)

# ----- Lazy imports (tolerate optional deps at import time) ------------------
def _lazy_uisp_service():
    from uisp_abstraction import UISPService  # provided in your repo
    return UISPService

def _lazy_fs_manager():
    # Use the manager + probe implementation from FS3900_inspector
    from FS3900_inspector import FS3900Manager  # exposes snapshot_all(...)
    return FS3900Manager

try:
    init_db()
except Exception as _e:
    import logging as _l; _l.getLogger(__name__).warning("DB init failed: %s", _e)

# =============================================================================
# NSM / CRM
# =============================================================================

def _fmt_dur(secs: float) -> str:
    secs = max(0, int(secs))
    h, r = divmod(secs, 3600)
    m, s = divmod(r, 60)
    return f"{h:02d}:{m:02d}:{s:02d}"

def persist_onu_states(olt_id: str, items: list[dict], *, now: datetime | None = None):
    """
    items: list of {"onu_id": str, "name": str, "address": str, "state": "online|offline|suspended"}
    """
    ts = now or datetime.now(timezone.utc)

    with session_scope() as s:
        # Load current states for this OLT
        existing = {
            (row.onu_id): row
            for row in s.query(OnuStatusCurrent).filter(OnuStatusCurrent.olt_id == olt_id)
        }

        seen = set()
        for it in items:
            onu_id = it["onu_id"]
            seen.add(onu_id)
            new_state = it["state"]
            name = it.get("name")
            address = it.get("address")
            cur = existing.get(onu_id)

            if cur is None:
                # new record
                cur = OnuStatusCurrent(
                    olt_id=olt_id, onu_id=onu_id, name=name, address=address, state=new_state, since=ts
                )
                s.add(cur)
                if new_state == "offline":
                    s.add(OnuOutage(olt_id=olt_id, onu_id=onu_id, name=name, address=address, start_ts=ts))
                continue

            # update name/address if improved
            if name and not cur.name:
                cur.name = name
            if address and not cur.address:
                cur.address = address

            if cur.state != new_state:
                # close or open outages
                if cur.state != "offline" and new_state == "offline":
                    s.add(OnuOutage(olt_id=olt_id, onu_id=onu_id, name=cur.name, address=cur.address, start_ts=ts))
                if cur.state == "offline" and new_state != "offline":
                    # close the latest open outage
                    last = (s.query(OnuOutage)
                            .filter(OnuOutage.olt_id == olt_id, OnuOutage.onu_id == onu_id, OnuOutage.end_ts.is_(None))
                            .order_by(OnuOutage.start_ts.desc()).first())
                    if last:
                        last.end_ts = ts
                cur.state = new_state
                cur.since = ts

        # Optionally, if some ONUs vanished entirely, you can mark them unknown here.


def run_uisp_lookup() -> None:
    """
    Poll NSM for ONUs per site; summarize & list disconnected/offline with
    name, street, outage start, and duration. Persists first-seen-offline in
    /var/lib/outage-monitor/offline_state.json so durations are meaningful.
    """
    UISPService = _lazy_uisp_service()
    svc = UISPService.from_config(settings=settings)

    # load persisted offline state
    offline_state: dict = _load_json(OFFLINE_STATE_PATH)
    now = time.time()

    sites_cfg: Dict[str, Dict[str, Any]] = getattr(settings, "LOCATIONS", {}) or {}
    site_count = len(sites_cfg)

    total_onus = 0
    total_disc_susp = 0
    total_disc_offline = 0

    # Collect current offline ids to expire healed ONUs at the end
    currently_offline_ids: set[str] = set()

    for human_site_name, site in sites_cfg.items():
        site_id = site.get("site")
        if not site_id:
            log.warning("Site %s missing 'site' id; skipping", human_site_name)
            continue

        try:
            onus: List[Dict[str, Any]] = svc.nsm.list_onus_by_site(site_id)
            # Normalize and persist ONU states (online/offline/suspended)
            items = []
            for o in onus:
                suspended = bool(o.get("suspended"))
                status = (o.get("status") or "").lower()
                if status == "disconnected" and suspended:
                    state = "suspended"
                elif status == "disconnected":
                    state = "offline"
                else:
                    state = "online"
                items.append({
                    "onu_id": str(o.get("id")),
                    "name": (o.get("name") or "").strip(),
                    "address": None,  # we don’t always have it; CRM enrichment happens later
                    "state": state,
                })
            persist_onu_states(olt_id=str(site_id), items=items)

        except Exception as e:
            log.error("NSM list_onus_by_site failed for %s (%s): %s", human_site_name, site_id, e)
            continue

        total_onus += len(onus)
        disc_susp = [o for o in onus if o.get("status") == "disconnected" and o.get("suspended")]
        active_offline = [o for o in onus if o.get("status") == "disconnected" and not o.get("suspended")]

        total_disc_susp += len(disc_susp)
        total_disc_offline += len(active_offline)

        # Emit per-site counts for quick eyeballing
        log.info("NSM [%s]: ONUs=%d, disconnected/suspended=%d, disconnected/offline=%d",
                 human_site_name, len(onus), len(disc_susp), len(active_offline))

        # Detail lines for active offline ONUs
        for onu in active_offline:
            onu_id   = str(onu.get("id"))
            name     = (onu.get("name") or onu_id or "").strip()
            currently_offline_ids.add(onu_id)

            # First-seen offline tracking
            first_ts = offline_state.get(onu_id, {}).get("first_offline_ts")
            if not isinstance(first_ts, (int, float)):
                first_ts = now
                offline_state[onu_id] = {
                    "first_offline_ts": first_ts,
                    "first_name": name,
                    "first_site": human_site_name,
                }
            duration = _fmt_dur(now - float(first_ts))

            # Try to enrich with CRM address data
            street = "unknown"
            try:
                client_id = svc.crm.find_client_by_name(name)
            except Exception as e:
                log.debug("CRM name->id lookup failed for %r: %s", name, e)
                client_id = None

            if client_id:
                try:
                    # We don't have a dedicated address helper, but the CRM client endpoint
                    # usually contains address fields; use the underlying _get.
                    details = svc.crm._get(f"clients/{client_id}")  # type: ignore[attr-defined]
                    # Heuristic picks: 'street1' or 'street', optionally city
                    street1 = details.get("street1") or details.get("street") or ""
                    city    = details.get("city") or ""
                    street  = f"{street1} {city}".strip() or "unknown"
                except Exception:
                    pass

            # Final detail line per ONU
            start_iso = datetime.fromtimestamp(float(first_ts), tz=timezone.utc).astimezone().isoformat(timespec="seconds")
            log.info("Offline ONU: %s | %s | start=%s | duration=%s", name, street, start_iso, duration)

    # Drop healed ONUs from state
    stale = [k for k in offline_state.keys() if k not in currently_offline_ids]
    for k in stale:
        offline_state.pop(k, None)
    _atomic_write_json(OFFLINE_STATE_PATH, offline_state)

    # One summary line like you requested
    log.info("NSM: OLTs scanned=%d, ONUs=%d, Disconnected/Suspended=%d, Disconnected/Offline=%d",
             site_count, total_onus, total_disc_susp, total_disc_offline)

# =============================================================================
# SQL Alchemy - Storage section
# =============================================================================

def persist_fs_snapshots(snapshots: dict[str, dict], *, now: datetime | None = None):
    """Store one polling round (in/out bps + DOM) into raw table."""
    ts = now or datetime.now(timezone.utc)
    rows: list[FsMetricSample] = []

    for key, snap in (snapshots or {}).items():
        host = snap.get("host")
        fs_label = None  # optional; fill if you keep a names map
        for p in snap.get("ports") or []:
            port = int(p.get("port"))
            ifname = f"ethernet 1/{port}"
            traffic = p.get("traffic") or {}
            dom = p.get("dom") or {}

            rows.append(FsMetricSample(
                ts=ts,
                fs_ip=host,
                fs_label=fs_label,
                port=port,
                ifname=ifname,
                in_bps=traffic.get("in_bps"),
                out_bps=traffic.get("out_bps"),
                temp_c=dom.get("temp_c"),
                vcc_v=dom.get("vcc_v"),
                bias_ma=dom.get("bias_ma"),
                tx_dbm=dom.get("tx_dbm"),
                rx_dbm=dom.get("rx_dbm"),
                key=key,
                kind="plain",  # or "link"/"olt_port" if you differentiate in your code
            ))

    if not rows:
        return

    with session_scope() as s:
        s.add_all(rows)

def _bucket_floor(ts: datetime, seconds: int) -> datetime:
    epoch = int(ts.timestamp())
    return datetime.fromtimestamp((epoch // seconds) * seconds, tz=ts.tzinfo)

def aggregate_5m(end_ts: datetime | None = None):
    """Compute last closed 5m bucket."""
    now = end_ts or datetime.now(timezone.utc)
    end_b = _bucket_floor(now, 300)      # e.g., 12:35:00
    start_b = end_b - timedelta(seconds=300)

    with session_scope() as s:
        # pull recent raw rows in the window
        rows = s.execute(
            """
            SELECT fs_ip, port,
                   in_bps, out_bps, temp_c, rx_dbm
            FROM fs_metric_sample
            WHERE ts >= :start_ts AND ts < :end_ts
            """,
            {"start_ts": start_b, "end_ts": end_b}
        ).all()

        if not rows:
            return

        # group in Python (SQLite has no native percentile)
        from collections import defaultdict
        g = defaultdict(list)
        for fs_ip, port, in_bps, out_bps, temp_c, rx_dbm in rows:
            g[(fs_ip, port)].append((in_bps, out_bps, temp_c, rx_dbm))

        def p95(vals: list[float]) -> float | None:
            vals = [v for v in vals if v is not None]
            if not vals:
                return None
            vals.sort()
            k = int(0.95 * (len(vals)-1))
            return vals[k]

        inserts = []
        for (fs_ip, port), samples in g.items():
            ins = [x[0] for x in samples if x[0] is not None]
            outs = [x[1] for x in samples if x[1] is not None]
            temps = [x[2] for x in samples if x[2] is not None]
            rxs = [x[3] for x in samples if x[3] is not None]

            row = FsMetricAgg5m(
                bucket_start=start_b,
                fs_ip=fs_ip,
                port=port,
                avg_in_bps=mean(ins) if ins else None,
                p95_in_bps=p95(ins) if ins else None,
                max_in_bps=max(ins) if ins else None,
                avg_out_bps=mean(outs) if outs else None,
                p95_out_bps=p95(outs) if outs else None,
                max_out_bps=max(outs) if outs else None,
                avg_temp_c=mean(temps) if temps else None,
                min_rx_dbm=min(rxs) if rxs else None,
                n=len(samples),
            )
            inserts.append(row)

        # upsert by PK (SQLite: INSERT OR REPLACE)
        for row in inserts:
            s.merge(row)  # merge obeys PK -> replace

def aggregate_60m(end_ts: datetime | None = None):
    now = end_ts or datetime.now(timezone.utc)
    end_b = _bucket_floor(now, 3600)
    start_b = end_b - timedelta(seconds=3600)

    with session_scope() as s:
        rows = s.execute(
            """
            SELECT bucket_start, fs_ip, port,
                   avg_in_bps, avg_out_bps, max_in_bps, max_out_bps,
                   avg_temp_c, min_rx_dbm, n
            FROM fs_metric_agg_5m
            WHERE bucket_start >= :start_ts AND bucket_start < :end_ts
            """,
            {"start_ts": start_b, "end_ts": end_b}
        ).all()
        if not rows:
            return

        from collections import defaultdict
        g = defaultdict(list)
        for b, fs_ip, port, ain, aout, maxin, maxout, atemp, minrx, n in rows:
            g[(fs_ip, port)].append((ain, aout, maxin, maxout, atemp, minrx, n))

        def wmean(values_counts):
            s_num = s_den = 0.0
            for v, n in values_counts:
                if v is not None and n:
                    s_num += v * n
                    s_den += n
            return (s_num / s_den) if s_den else None

        inserts = []
        for (fs_ip, port), samples in g.items():
            avg_in = wmean([(x[0], x[6]) for x in samples])
            avg_out = wmean([(x[1], x[6]) for x in samples])
            max_in = max([x[2] for x in samples if x[2] is not None], default=None)
            max_out = max([x[3] for x in samples if x[3] is not None], default=None)
            avg_temp = wmean([(x[4], x[6]) for x in samples])
            min_rx = min([x[5] for x in samples if x[5] is not None], default=None)
            total_n = sum([x[6] for x in samples if x[6]])

            inserts.append(FsMetricAgg60m(
                bucket_start=start_b,
                fs_ip=fs_ip,
                port=port,
                avg_in_bps=avg_in,
                p95_in_bps=None,  # optional: compute p95 directly from raw, or leave null
                max_in_bps=max_in,
                avg_out_bps=avg_out,
                p95_out_bps=None,
                max_out_bps=max_out,
                avg_temp_c=avg_temp,
                min_rx_dbm=min_rx,
                n=total_n,
            ))
        for row in inserts:
            s.merge(row)

def retention_housekeeping():
    from config import settings
    raw_days = int(getattr(settings, "RAW_SAMPLE_RETENTION_DAYS", 14))
    agg_days = int(getattr(settings, "AGG_RETENTION_DAYS", 120))
    now = datetime.now(timezone.utc)

    with session_scope() as s:
        s.execute(
            "DELETE FROM fs_metric_sample WHERE ts < :cutoff",
            {"cutoff": now - timedelta(days=raw_days)}
        )
        s.execute(
            "DELETE FROM fs_metric_agg_5m WHERE bucket_start < :cutoff",
            {"cutoff": now - timedelta(days=agg_days)}
        )
        s.execute(
            "DELETE FROM fs_metric_agg_60m WHERE bucket_start < :cutoff",
            {"cutoff": now - timedelta(days=agg_days)}
        )


# =============================================================================
# FS3900 traffic (bps)
# =============================================================================

_IFNAME_PORT_RE = re.compile(r"/\s*(\d+)\s*$")  # e.g. "ethernet 1/25" -> 25

def _port_from_ifname(ifname: str) -> Optional[int]:
    m = _IFNAME_PORT_RE.search(str(ifname))
    return int(m.group(1)) if m else None

def run_fs3900_scan(sleep_s: Optional[float] = None) -> None:
    """
    Poll FS3900 links (inter-switch + OLT uplinks) and log bps.
    Sources:
      - settings.FS_LINK_PROBES[*].{olt_id, fs_ip, fs_ifname}
      - settings.OLT_PROBES[olt_uuid].{name, fs_ip, fs_uplink_ifname}
    """
    FS3900Manager = _lazy_fs_manager()
    pacing = float(sleep_s if sleep_s is not None else getattr(settings, "FS_TRAFFIC_SLEEP_S", 1.5))

    link_probes: List[Dict[str, Any]] = list(getattr(settings, "FS_LINK_PROBES", []) or [])
    olt_probes: Dict[str, Dict[str, Any]] = dict(getattr(settings, "OLT_PROBES", {}) or {})
    fs_names: Dict[str, str] = dict(getattr(settings, "FS_NAMES", {}) or {})

    if not link_probes and not olt_probes:
        log.warning("FS scan: no probes configured; nothing to do")
        return

    # --- Build mapping of snapshot keys -> switch IP, and figure out the union of ports we need
    fs_by_olt: Dict[str, str] = {}
    wanted_ports_by_fs: Dict[str, set[int]] = {}

    # helper to parse "ethernet 1/25" -> 25
    def _p(ifname: Any) -> Optional[int]:
        m = _IFNAME_PORT_RE.search(str(ifname))
        return int(m.group(1)) if m else None

    def _find_port_entry(snap: dict, port: int) -> dict | None:
        for p in snap.get("ports") or []:
            pno = int(p.get("port", -1)) if isinstance(p, dict) else int(getattr(p, "port", -1))
            if pno == port:
                return p if isinstance(p, dict) else p.__dict__
        return None

    # Inter-FS links
    for link in link_probes:
        oid = str(link.get("olt_id") or link.get("fs_ip") or "")
        fs_ip = link.get("fs_ip")
        port = _p(link.get("fs_ifname"))
        if fs_ip:
            fs_by_olt[oid] = fs_ip
            if port is not None:
                wanted_ports_by_fs.setdefault(fs_ip, set()).add(port)

    # OLT-facing uplinks
    for olt_id, rec in olt_probes.items():
        fs_ip = rec.get("fs_ip")
        port = _p(rec.get("fs_uplink_ifname"))
        if fs_ip:
            fs_by_olt[olt_id] = fs_ip
            if port is not None:
                wanted_ports_by_fs.setdefault(fs_ip, set()).add(port)

    if not fs_by_olt:
        log.warning("FS scan: could not derive FS hosts from config")
        return

    # Manager uses a single port list for all probes; give it the union.
    # (Per-switch filtering happens when we log, so extra sampled ports are harmless.)
    union_ports = sorted({p for ports in wanted_ports_by_fs.values() for p in ports})
    if not union_ports:
        log.warning("FS scan: no parsable ports in config")
        return

    mgr = FS3900Manager(
        community=getattr(settings, "SNMP_COMMUNITY", "public"),
        timeout_s=float(getattr(settings, "SNMP_TIMEOUT_S", 0.6)),
        retries=int(getattr(settings, "SNMP_RETRIES", 1)),
        max_workers=int(getattr(settings, "FS_MAX_WORKERS", 6)),
        debug=False,
    )
    mgr.fs_by_olt = fs_by_olt  # override mapping (keys are link IDs or OLT UUIDs)
    mgr.ports = tuple(union_ports)

    # Take snapshots with traffic
    snapshots: Dict[str, Dict[str, Any]] = mgr.snapshot_all(sample_traffic=True, traffic_sleep_s=pacing) or {}
    # and SAVE
    persist_fs_snapshots(snapshots)
    # Build (snapshot_key, port) -> (in_bps, out_bps)
    metrics: Dict[Tuple[str, int], Tuple[Optional[float], Optional[float]]] = {}
    for key, snap in snapshots.items():
        for p in snap.get("ports") or []:
            if isinstance(p, dict):
                port_no = int(p.get("port", -1))
                t = p.get("traffic") or {}
                in_bps, out_bps = t.get("in_bps"), t.get("out_bps")
            else:
                port_no = int(getattr(p, "port", -1))
                t = getattr(p, "traffic", None)
                in_bps = getattr(t, "in_bps", None) if t else None
                out_bps = getattr(t, "out_bps", None) if t else None
            if port_no >= 0:
                metrics[(key, port_no)] = (in_bps, out_bps)

    # --- Emit logs for inter-FS links
    for link in link_probes:
        key = str(link.get("olt_id") or link.get("fs_ip"))
        fs_ip = str(link.get("fs_ip"))
        ifname = str(link.get("fs_ifname"))
        port = _port_from_ifname(ifname)
        if not (key and fs_ip and port is not None):
            continue

        snap = snapshots.get(key) or {}
        # find the port entry to see if mapping worked
        port_entry = None
        for p in snap.get("ports") or []:
            pno = int(p.get("port", -1)) if isinstance(p, dict) else int(getattr(p, "port", -1))
            if pno == port:
                port_entry = p if isinstance(p, dict) else p.__dict__
                break

        if not port_entry:
            log.error("FS %s %s: snapshot missing entry (mapping failed?)", fs_ip, ifname)
            continue

        if_idx = port_entry.get("ifindex")
        t = port_entry.get("traffic") or {}
        in_bps, out_bps = t.get("in_bps"), t.get("out_bps")

        if if_idx is None:
            log.error("FS %s %s: no ifIndex mapping; set FS_DEBUG=1 and check ifName/ifDescr labels", fs_ip, ifname)
        elif in_bps is None or out_bps is None:
            log.error("FS %s %s (ifIndex %s): counters unavailable; check SNMP ACL/community/VRF/firmware", fs_ip,
                      ifname, if_idx)
        else:
            fs_label = fs_names.get(fs_ip, fs_ip)
            log.info("FS %s (%s) %s: in=%.0f bps out=%.0f bps (sleep=%.3fs)", fs_ip, fs_label, ifname, in_bps, out_bps,
                     pacing)

        snap = snapshots.get(key) or {}
        port_entry = _find_port_entry(snap, port)
        dom = (port_entry or {}).get("dom") or {}
        worst = (port_entry or {}).get("worst_severity")
        # Log DOM only on optics ports (25–28)
        if 25 <= port <= 28 and dom:
            log.info(
                "FS %s (%s) %s DOM: temp=%.1fC vcc=%.3fV bias=%.2f mA tx=%.2f dBm rx=%.2f dBm%s",
                fs_ip, fs_label, ifname,
                float(dom.get("temp_c", float("nan"))),
                float(dom.get("vcc_v", float("nan"))),
                float(dom.get("bias_ma", float("nan"))),
                float(dom.get("tx_dbm", float("nan"))),
                float(dom.get("rx_dbm", float("nan"))),
                f" | sev={worst}" if worst else ""
            )

        # If DOM is present and worst severity computed, log it too:
        worst = port_entry.get("worst_severity")
        if worst:
            log.info("FS %s %s DOM severity: %s", fs_ip, ifname, worst)

    # --- Emit logs for OLT-facing uplinks
    for olt_id, rec in olt_probes.items():
        fs_ip  = str(rec.get("fs_ip"))
        ifname = str(rec.get("fs_uplink_ifname"))
        port   = _p(ifname)
        if not (fs_ip and port is not None):
            continue
        in_bps, out_bps = metrics.get((olt_id, port), (None, None))
        fs_label = fs_names.get(fs_ip, fs_ip)
        name = rec.get("name") or olt_id
        if in_bps is None or out_bps is None:
            log.warning("Uplink %s | FS %s (%s) %s: no traffic sample (port %s)",
                        name, fs_ip, fs_label, ifname, port)
        else:
            log.info("Uplink %s | FS %s (%s) %s: in=%.0f bps out=%.0f bps (sleep=%.3fs)",
                     name, fs_ip, fs_label, ifname, in_bps, out_bps, pacing)
        snap = snapshots.get(olt_id) or {}
        port_entry = _find_port_entry(snap, port)
        dom = (port_entry or {}).get("dom") or {}
        worst = (port_entry or {}).get("worst_severity")
        if 25 <= port <= 28 and dom:
            log.info(
                "Uplink %s | FS %s (%s) %s DOM: temp=%.1fC vcc=%.3fV bias=%.2f mA tx=%.2f dBm rx=%.2f dBm%s",
                name, fs_ip, fs_label, ifname,
                float(dom.get("temp_c", float("nan"))),
                float(dom.get("vcc_v", float("nan"))),
                float(dom.get("bias_ma", float("nan"))),
                float(dom.get("tx_dbm", float("nan"))),
                float(dom.get("rx_dbm", float("nan"))),
                f" | sev={worst}" if worst else ""
            )
    log.info("FS scan: done")

@app.task(name="db.rollup_5m", queue="poll")
def rollup_5m_task():
    aggregate_5m()

@app.task(name="db.rollup_60m", queue="poll")
def rollup_60m_task():
    aggregate_60m()

@app.task(name="db.retention", queue="poll")
def retention_task():
    retention_housekeeping()

# =============================================================================
# If you like running this file directly for quick tests
# =============================================================================
if __name__ == "__main__":
    import sys
    mode = (sys.argv[1] if len(sys.argv) > 1 else "nsm").lower()
    if mode in ("nsm", "uisp"):
        run_uisp_lookup()
    elif mode in ("fs", "fs3900"):
        run_fs3900_scan(sleep_s=float(getattr(settings, "FS_TRAFFIC_SLEEP_S", 1.5)))
    else:
        print("Usage: python tasks.py [nsm|fs]")
