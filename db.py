from __future__ import annotations
import os
from contextlib import contextmanager
from sqlalchemy import create_engine, event
from sqlalchemy.orm import sessionmaker
from sqlalchemy.engine import make_url
from config import settings

DB_URL = getattr(settings, "DATABASE_URL", "sqlite:////var/lib/outage-monitor/monitor.db")

url = make_url(DB_URL)
connect_args = {}

# Normalize SQLite path and ensure parent dir exists
if url.drivername.startswith("sqlite"):
    # SQLAlchemy gives us .database as a filesystem path (may be None for memory)
    db_path = url.database
    if not db_path:
        # e.g. sqlite:///:memory:  -> nothing to do
        pass
    else:
        # If someone set a relative path (three slashes), relocate it under /var/lib/outage-monitor
        if not os.path.isabs(db_path):
            base = "/var/lib/outage-monitor"
            os.makedirs(base, exist_ok=True)
            db_path = os.path.join(base, db_path.lstrip(os.sep))
            DB_URL = f"sqlite:////{db_path}"
            url = make_url(DB_URL)
        # Ensure parent dir exists for absolute paths too
        os.makedirs(os.path.dirname(db_path), exist_ok=True)
        connect_args = {"check_same_thread": False}

engine = create_engine(DB_URL, future=True, pool_pre_ping=True, connect_args=connect_args)
SessionLocal = sessionmaker(bind=engine, autoflush=False, autocommit=False, future=True)

@event.listens_for(engine, "connect")
def _set_sqlite_pragmas(dbapi_connection, connection_record):
    if url.drivername.startswith("sqlite"):
        cur = dbapi_connection.cursor()
        cur.execute("PRAGMA journal_mode=WAL;")
        cur.execute("PRAGMA synchronous=NORMAL;")
        cur.close()

@contextmanager
def session_scope():
    session = SessionLocal()
    try:
        yield session
        session.commit()
    except Exception:
        session.rollback()
        raise
    finally:
        session.close()

def init_db():
    from models import Base
    Base.metadata.create_all(bind=engine)
