from __future__ import annotations

import os
import re
import time
import subprocess
import logging
from dataclasses import dataclass, asdict
from typing import Dict, List, Tuple, Optional, Iterable, Any
from concurrent.futures import ThreadPoolExecutor, as_completed

log = logging.getLogger(__name__)

# ----------------------------
# IF-MIB bases
# ----------------------------
IFNAME   = "1.3.6.1.2.1.31.1.1.1.1"   # ifName
IFDESCR  = "1.3.6.1.2.1.2.2.1.2"       # ifDescr
IFALIAS  = "1.3.6.1.2.1.31.1.1.1.18"   # ifAlias
IFOPER   = "1.3.6.1.2.1.2.2.1.8"       # ifOperStatus: 1=up,2=down,3=testing...
IFHC_IN  = "1.3.6.1.2.1.31.1.1.1.6"    # ifHCInOctets
IFHC_OUT = "1.3.6.1.2.1.31.1.1.1.10"   # ifHCOutOctets
IF32_IN  = "1.3.6.1.2.1.2.2.1.10"      # ifInOctets
IF32_OUT = "1.3.6.1.2.1.2.2.1.16"      # ifOutOctets

# ----------------------------
# FS3900 vendor subtree
# ----------------------------
FS_BASE        = "1.3.6.1.4.1.52642.2.1.45.1"
DOM_VAL_BASE   = FS_BASE + ".2.11.1"   # strings with units/state; columns below
THR_BASE       = FS_BASE + ".2.12.1"   # thresholds table (optional wire-up)

# DOM columns under DOM_VAL_BASE
# e.g. ...11.1.2.<port> = "35.36 degrees C normal"
#      ...11.1.3.<port> = "3.34 V normal"
#      ...11.1.4.<port> = "78.35 mA normal"
# Some builds expose optical power as 5/6 (dBm)
DOM_FIELDS = {
    2: "temp_c",
    3: "vcc_v",
    4: "bias_ma",
    5: "tx_dbm",
    6: "rx_dbm",
}

_FLOAT_RX = re.compile(r'(-?\d+(?:\.\d+)?)')

@dataclass(frozen=True)
class DomLive:
    temp_c:  Optional[float]
    vcc_v:   Optional[float]
    bias_ma: Optional[float]
    tx_dbm:  Optional[float]
    rx_dbm:  Optional[float]
    raw_state: Optional[str]

@dataclass(frozen=True)
class PortThresholds:
    # Generic placeholder; extend with vendor specifics if you want threshold logic
    temp_c_warn_hi: Optional[float] = None
    temp_c_alarm_hi: Optional[float] = None
    temp_c_warn_lo: Optional[float] = None
    temp_c_alarm_lo: Optional[float] = None
    rx_dbm_warn_lo: Optional[float] = None
    rx_dbm_alarm_lo: Optional[float] = None
    tx_dbm_warn_lo: Optional[float] = None
    tx_dbm_alarm_lo: Optional[float] = None
    bias_ma_warn_hi: Optional[float] = None
    bias_ma_alarm_hi: Optional[float] = None
    vcc_v_warn_hi: Optional[float] = None
    vcc_v_alarm_hi: Optional[float] = None
    vcc_v_warn_lo: Optional[float] = None
    vcc_v_alarm_lo: Optional[float] = None

@dataclass(frozen=True)
class TrafficSample:
    in_bps:  Optional[float]
    out_bps: Optional[float]
    dt_ms:   int

class FS3900Probe:
    """
    SNMP probe for one FS3900-family switch.
    """
    def __init__(self,
                 host: str,
                 community: str,
                 *,
                 ports: Iterable[int] = (25,26,27,28),
                 timeout_s: float = 0.8,
                 retries: int = 1) -> None:
        self.host = host
        self.community = community
        self.ports = tuple(int(p) for p in ports)
        self.timeout_s = timeout_s
        self.retries = retries

    # ---------------- SNMP helpers ----------------

    def _snmpwalk(self, oid: str) -> List[str]:
        cmd = ["snmpwalk", "-v2c", "-c", self.community, "-On", self.host, oid]
        try:
            out = subprocess.check_output(cmd, stderr=subprocess.DEVNULL,
                                          timeout=self.timeout_s * (self.retries + 1)).decode()
            return [ln.strip() for ln in out.splitlines() if ln.strip()]
        except Exception:
            return []

    def _snmpget_many(self, oids: Iterable[str]) -> Dict[str, Optional[str]]:
        oid_list = list(oids)
        if not oid_list:
            return {}
        cmd = ["snmpget", "-v2c", "-c", self.community, "-On", "-Oqv", self.host] + oid_list
        try:
            out = subprocess.check_output(cmd, stderr=subprocess.DEVNULL,
                                          timeout=self.timeout_s * (self.retries + 1)).decode()
            lines = [ln.strip() for ln in out.splitlines()]
            return dict(zip(oid_list, lines))
        except Exception:
            return {oid: None for oid in oid_list}

    # ---------------- Mapping ----------------

    def _assume_ifindex_eq_port(self) -> bool:
        """Sanity check that ifDescr.<port> exists for one port; if so, treat ifIndex==port."""
        probe_port = next(iter(self.ports), None)
        if probe_port is None:
            return False
        test = self._snmpget_many([f"{IFDESCR}.{probe_port}"])
        val = list(test.values())[0]
        return bool(val)

    def _resolve_ports_to_ifindex(self) -> Dict[int, int]:
        # Fast-path: many FS builds have ifIndex == physical port number
        if self._assume_ifindex_eq_port():
            return {p: p for p in self.ports}

        # Fallback: walk labels and extract trailing port number
        def parse_map(lines: List[str]) -> Dict[int, str]:
            out: Dict[int, str] = {}
            for ln in lines:
                m = re.search(r'(?:\.|\:)(\d+)\s*=\s*(?:\w+\s*:\s*)?"?([^"\n]+)"?$', ln)
                if not m:
                    continue
                idx = int(m.group(1))
                lab = m.group(2).strip()
                out[idx] = lab
            return out

        name_by_idx = parse_map(self._snmpwalk(IFNAME))
        descr_by_idx = parse_map(self._snmpwalk(IFDESCR))
        alias_by_idx = parse_map(self._snmpwalk(IFALIAS))

        def norm(lbl: str) -> str:
            s = lbl.strip().lower()
            s = s.replace("ethernet", "")
            s = re.sub(r"[-_]", " ", s)
            s = re.sub(r"\s+", " ", s)
            return s

        def extract_port(lbl: str) -> Optional[int]:
            s = norm(lbl)
            m = re.search(r"/\s*(\d+)(?!.*/\s*\d+)", s)
            if m:
                return int(m.group(1))
            m = re.search(r"(?:^|\s)(?:port\s*)?(\d+)\s*$", s)
            return int(m.group(1)) if m else None

        mapping: Dict[int, int] = {}
        for src in (name_by_idx, descr_by_idx, alias_by_idx):
            for idx, lbl in src.items():
                p = extract_port(lbl)
                if p in self.ports and p not in mapping:
                    mapping[p] = idx

        # last-resort fallback
        if not mapping:
            mapping = {p: p for p in self.ports}
        return mapping

    # ---------------- Counters ----------------

    def _read_octets(self, ifindex: int, use_64bit: bool = True) -> Tuple[Optional[int], Optional[int]]:
        if use_64bit:
            oids = [f"{IFHC_IN}.{ifindex}", f"{IFHC_OUT}.{ifindex}"]
        else:
            oids = [f"{IF32_IN}.{ifindex}", f"{IF32_OUT}.{ifindex}"]
        vals = self._snmpget_many(oids)
        try:
            in_oct = int((vals.get(oids[0]) or "").split()[0])
            out_oct = int((vals.get(oids[1]) or "").split()[0])
        except Exception:
            in_oct = out_oct = None
        return in_oct, out_oct

    def _measure_bps(self, ifindex: int, sleep_s: float) -> TrafficSample:
        in1, out1 = self._read_octets(ifindex, use_64bit=True)
        if in1 is None or out1 is None:
            in1, out1 = self._read_octets(ifindex, use_64bit=False)
        t1 = time.time()
        time.sleep(max(0.001, sleep_s))
        in2, out2 = self._read_octets(ifindex, use_64bit=(in1 is not None and out1 is not None))
        t2 = time.time()
        dt = max(0.001, t2 - t1)

        def diff(c1: Optional[int], c2: Optional[int], bits: int) -> Optional[int]:
            if c1 is None or c2 is None:
                return None
            if c2 >= c1:
                return c2 - c1
            # wrap
            mod = 1 << bits
            return (c2 + mod) - c1

        if in1 is not None and out1 is not None and in2 is not None and out2 is not None:
            bits = 64
        else:
            # fallback: read 32-bit again to be consistent
            in1_32, out1_32 = self._read_octets(ifindex, use_64bit=False)
            time.sleep(max(0.001, sleep_s))
            in2_32, out2_32 = self._read_octets(ifindex, use_64bit=False)
            in1, out1, in2, out2 = in1_32, out1_32, in2_32, out2_32
            bits = 32

        din = diff(in1, in2, bits)
        dout = diff(out1, out2, bits)

        in_bps = (din * 8 / dt) if din is not None else None
        out_bps = (dout * 8 / dt) if dout is not None else None
        return TrafficSample(in_bps=in_bps, out_bps=out_bps, dt_ms=int(dt * 1000))

    # ---------------- DOM collection ----------------

    def _snmp_walk_pairs(self, oid_prefix: str) -> List[Tuple[str, str]]:
        cmd = ["snmpwalk", "-v2c", "-c", self.community, "-On", self.host, oid_prefix]
        try:
            out = subprocess.check_output(cmd, stderr=subprocess.DEVNULL,
                                          timeout=self.timeout_s * (self.retries + 1)).decode()
        except Exception:
            return []
        pairs: List[Tuple[str, str]] = []
        for line in out.splitlines():
            if " = " not in line:
                continue
            oid, rhs = line.split(" = ", 1)
            val = rhs.split(":", 1)[-1].strip()
            pairs.append((oid.strip(), val))
        return pairs

    def _parse_first_float(self, s: str) -> Optional[float]:
        s = (s or "").strip().strip('"')
        m = _FLOAT_RX.search(s)
        return float(m.group(1)) if m else None

    def _parse_state_word(self, s: str) -> Optional[str]:
        s = (s or "").strip().strip('"')
        if not s:
            return None
        parts = s.split()
        return parts[-1].lower() if parts else None

    def _collect_dom_live(self) -> Dict[int, DomLive]:
        per_port: Dict[int, Dict[str, Optional[float]]] = {p: {} for p in self.ports}
        port_state: Dict[int, str] = {}

        for fnum, key in DOM_FIELDS.items():
            for oid, raw in self._snmp_walk_pairs(f"{DOM_VAL_BASE}.{fnum}"):
                try:
                    port = int(oid.rsplit(".", 1)[-1])
                except ValueError:
                    continue
                if port not in per_port:
                    continue
                val = self._parse_first_float(raw)
                if val is not None:
                    per_port[port][key] = val
                st = self._parse_state_word(raw)
                if st:
                    port_state[port] = st

        result: Dict[int, DomLive] = {}
        for port in self.ports:
            vals = per_port.get(port, {})
            result[port] = DomLive(
                temp_c=vals.get("temp_c"),
                vcc_v=vals.get("vcc_v"),
                bias_ma=vals.get("bias_ma"),
                tx_dbm=vals.get("tx_dbm"),
                rx_dbm=vals.get("rx_dbm"),
                raw_state=port_state.get(port),
            )
        return result

    def _collect_thresholds(self) -> Dict[int, PortThresholds]:
        # Wire up THR_BASE columns here if you want severity from thresholds.
        # For now, provide empty thresholds so snapshot shape is consistent.
        return {p: PortThresholds() for p in self.ports}

    # ---------------- Severity classification (optional) ----------------

    def _classify_all(self, dom: Dict[str, Any], thr: Dict[str, Any]) -> Dict[str, str]:
        """
        Minimal placeholder; returns 'OK' for populated metrics.
        Extend with comparisons against 'thr' once threshold OIDs are mapped.
        """
        return {k: "OK" for k in ("temp_c", "vcc_v", "bias_ma", "tx_dbm", "rx_dbm") if dom.get(k) is not None}

    def _worst_severity(self, sev_map: Dict[str, str]) -> str:
        order = {"ALARM": 3, "WARN": 2, "OK": 1}
        worst = "OK"
        for s in sev_map.values():
            if order.get(s, 1) > order.get(worst, 1):
                worst = s
        return worst

    # ---------------- Snapshot ----------------

    def snapshot(self, *, sample_traffic: bool = True, traffic_sleep_s: float = 1.5) -> Dict[str, Any]:
        """
        Returns:
          {"host": "<ip>", "ports": [
             {"port":25,"ifindex":25,"oper_up":1|0|None,
              "traffic":{"in_bps":...,"out_bps":...,"dt_ms":...},
              "dom":{...}, "thresholds":{...}, "worst_severity":"OK|WARN|ALARM"}
          ]}
        """
        port_to_ifindex = self._resolve_ports_to_ifindex()

        # oper status
        oper_by_idx: Dict[int, Optional[int]] = {}
        if port_to_ifindex:
            oids = [f"{IFOPER}.{idx}" for idx in port_to_ifindex.values()]
            vals = self._snmpget_many(oids)
            for oid, val in vals.items():
                try:
                    idx = int(str(oid).split(".")[-1])
                except Exception:
                    continue
                try:
                    oper = int(str(val).split()[0]) if val else None
                except Exception:
                    oper = None
                oper_by_idx[idx] = oper

        # DOM/Thresholds collected once
        dom_by_port = self._collect_dom_live()
        thr_by_port = self._collect_thresholds()

        ports_out: List[Dict[str, Any]] = []
        for port in self.ports:
            ifindex = port_to_ifindex.get(port)
            oper_up: Optional[int] = None
            if ifindex in oper_by_idx:
                oper_up = 1 if oper_by_idx[ifindex] == 1 else 0 if oper_by_idx[ifindex] == 2 else None

            traffic = {"in_bps": None, "out_bps": None, "dt_ms": 0}
            if ifindex is not None and sample_traffic:
                sample = self._measure_bps(ifindex, traffic_sleep_s)
                traffic = {"in_bps": sample.in_bps, "out_bps": sample.out_bps, "dt_ms": sample.dt_ms}

            entry: Dict[str, Any] = {
                "port": port,
                "ifindex": ifindex,
                "oper_up": oper_up,
                "traffic": traffic,
            }

            dom_obj = dom_by_port.get(port)
            if dom_obj:
                entry["dom"] = asdict(dom_obj)

            thr_obj = thr_by_port.get(port)
            if thr_obj:
                entry["thresholds"] = asdict(thr_obj)

            if dom_obj and thr_obj:
                try:
                    sev_map = self._classify_all(entry["dom"], entry["thresholds"])
                    entry["worst_severity"] = self._worst_severity(sev_map)
                except Exception:
                    pass

            ports_out.append(entry)

        if os.getenv("FS_DEBUG"):
            log.info("FS %s port->ifIndex: %s", self.host, port_to_ifindex)

        return {"host": self.host, "ports": ports_out}


class FS3900Manager:
    """
    Polls multiple FS3900 devices concurrently and returns snapshots keyed by OLT/link id.
    """
    def __init__(self,
                 community: str,
                 *,
                 fs_by_olt: Optional[Dict[str, str]] = None,
                 ports: Iterable[int] = (25,26,27,28),
                 timeout_s: float = 0.8,
                 retries: int = 1,
                 max_workers: int = 6,
                 debug: bool = False) -> None:
        self.community = community
        self.fs_by_olt = fs_by_olt or {}
        self.ports = tuple(int(p) for p in ports)
        self.timeout_s = timeout_s
        self.retries = retries
        self.max_workers = max_workers
        self.debug = debug

    def snapshot_all(self, *, sample_traffic: bool = True, traffic_sleep_s: float = 1.5) -> Dict[str, Dict[str, Any]]:
        results: Dict[str, Dict[str, Any]] = {}
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            futures = {}
            for olt_id, fs_ip in self.fs_by_olt.items():
                probe = FS3900Probe(
                    host=fs_ip,
                    community=self.community,
                    ports=self.ports,
                    timeout_s=self.timeout_s,
                    retries=self.retries,
                )
                futures[executor.submit(probe.snapshot, sample_traffic=sample_traffic, traffic_sleep_s=traffic_sleep_s)] = olt_id

            for fut in as_completed(futures):
                key = futures[fut]
                try:
                    results[key] = fut.result()
                    if self.debug:
                        log.info("Polled %s (%s): %d ports", key, results[key].get("host"), len(results[key].get("ports", [])))
                except Exception as e:
                    log.warning("Failed to poll %s (%s): %s", key, self.fs_by_olt.get(key), e)
        return results
