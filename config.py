# config.py  — release-safe, FHS-friendly settings
from __future__ import annotations
import os
from pathlib import Path
from dataclasses import dataclass, field

# --- optional .env loader for local/dev (systemd supplies env in prod) -----
try:
    from dotenv import load_dotenv  # type: ignore
    load_dotenv()
except Exception:
    # Soft-fail: no python-dotenv in prod is fine.
    pass

# ----------------------- helpers & directories ------------------------------

def _env(name: str, default: str | None = None) -> str:
    v = os.getenv(name)
    if v is None:
        if default is None:
            raise RuntimeError(f"Missing required environment variable: {name}")
        return default
    return v

def _env_list(name: str) -> list[str]:
    val = os.getenv(name, "")
    return [s.strip() for s in val.split(",") if s.strip()]

def _env_path(name: str, default: str) -> Path:
    return Path(os.getenv(name, default)).expanduser().resolve()

def _ensure_dir(p: Path, mode: int = 0o750) -> Path:
    p.mkdir(parents=True, exist_ok=True)
    try:
        os.chmod(p, mode)
    except PermissionError:
        pass
    return p

# FHS layout (override via env if ever needed)
ETC_DIR   = _env_path("ETC_DIR",   "/etc/outage-monitor")
STATE_DIR = _ensure_dir(_env_path("STATE_DIR", "/var/lib/outage-monitor"))
LOG_DIR   = _ensure_dir(_env_path("LOG_DIR",   "/var/log/outage-monitor"))
RUN_DIR   = _ensure_dir(_env_path("RUN_DIR",   "/run/outage-monitor"), mode=0o755)

# Forbid writes inside release directories
def _assert_paths_sane(paths: list[Path]) -> None:
    for p in paths:
        if str(p).startswith("/opt/outage-monitor"):
            raise RuntimeError(f"Invalid writable path under /opt: {p}")

def _git_rev() -> str:
    rev = os.getenv("GIT_REV")
    if rev:
        return rev
    # optional file written by your deploy script
    rel = Path("/opt/outage-monitor/current/.release")
    if rel.exists():
        try:
            for line in rel.read_text(encoding="utf-8").splitlines():
                if line.startswith("GIT_REV="):
                    return line.split("=", 1)[1].strip()
        except Exception:
            pass
    return "dev"

# ------------------------------ settings ------------------------------------

@dataclass(frozen=True)
class Settings:
    # App/runtime
    APP_ENV: str = os.getenv("APP_ENV", "prod")
    TIMEZONE: str = os.getenv("TIMEZONE", "America/New_York")
    LOG_LEVEL: str = os.getenv("LOG_LEVEL", "INFO")
    APP_VERSION: str = _git_rev()

    # NSM / CRM (required in prod; left optional for dev)
    UISP_BASE_URL: str = os.getenv("UISP_BASE_URL", "https://esvc.uisp.com").rstrip("/")
    NSM_BASE_URL: str = os.getenv("NSM_BASE_URL", "https://esvc.uisp.com/nms/api/v2.1").rstrip("/")
    NSM_TOKEN: str | None = os.getenv("NSM_TOKEN")
    CRM_BASE_URL: str = os.getenv("CRM_BASE_URL", "https://esvc.uisp.com/api/v1.0").rstrip("/")
    CRM_TOKEN: str | None = os.getenv("CRM_TOKEN")
    USE_CRM_FALLBACK: bool = os.getenv("USE_CRM_FALLBACK", "true").lower() == "true"
    CRM_LOOKUPS_PER_POLL: int = int(os.getenv("CRM_LOOKUPS_PER_POLL", "50"))

    # Celery (your canonical names)
    CELERY_BROKER_URL: str = os.getenv("CELERY_BROKER_URL", "redis://localhost:6379/0")
    CELERY_RESULT_BACKEND: str = os.getenv("CELERY_RESULT_BACKEND", "redis://localhost:6379/0")
    CELERY_TASK_SERIALIZER: str = os.getenv("CELERY_TASK_SERIALIZER", "json")
    CELERY_RESULT_SERIALIZER: str = os.getenv("CELERY_RESULT_SERIALIZER", "json")
    CELERY_ACCEPT_CONTENT: list[str] = field(default_factory=lambda: ["json"])
    CELERY_TIMEZONE: str = os.getenv("CELERY_TIMEZONE", "America/New_York")

    # Test / sandbox
    TEST_MODE: int = int(os.getenv("TEST_MODE", "0"))
    TEST_SMS_TO: list[str] = field(default_factory=lambda: _env_list("TEST_SMS_TO") or ["+17576958080"])
    TEST_EMAIL_TO: str = os.getenv("TEST_EMAIL_TO", "<EMAIL>")
    DRY_RUN_NOTIFICATIONS: bool = os.getenv("DRY_RUN_NOTIFICATIONS", "false").lower() == "true"

    # Regions & polling
    REGION_KEYS: str = os.getenv("REGION_KEYS", "wtn,ccp,wts,off,wl")
    # Poll cadences (seconds)
    NSM_POLL_INTERVAL_SECS = 30


    # Per-device timers / traffic
    PER_DEVICE_THRESHOLD_MIN: int = int(os.getenv("PER_DEVICE_NOTIFY_AFTER_MIN", "3"))
    PER_DEVICE_RESTORE_STABLE_MIN: int = int(os.getenv("PER_DEVICE_RESTORE_STABLE_MIN", "3"))
    FLAP_SUPPRESS_MIN: int = int(os.getenv("FLAP_SUPPRESS_MIN", "5"))
    TRAFFIC_BPS_THRESHOLD: int = int(os.getenv("TRAFFIC_BPS_THRESHOLD", "100000"))
    FS_TRAFFIC_SLEEP_S: float = 1.5
    FS_MIN_DT_MS: int = 1200
    FS_POLL_INTERVAL_SECS = 90

    # Cluster rules
    CLUSTER_WINDOW_SECS: int = int(os.getenv("CLUSTER_WINDOW_SECS", "120"))
    CLUSTER_THRESHOLD: int = int(os.getenv("CLUSTER_THRESHOLD_N", "5"))
    CLUSTER_URGENT_M: int = int(os.getenv("CLUSTER_URGENT_M", "10"))
    FIRST_ESCALATION_MIN: int = int(os.getenv("FIRST_ESCALATION_MIN", "10"))
    CLUSTER_RESTORE_STABLE_MIN: int = int(os.getenv("CLUSTER_RESTORE_STABLE_MIN", "3"))
    SITE_CRITICAL_MINUTES: int = int(os.getenv("SITE_CRITICAL_MINUTES", "20"))

    # Notification policy
    NOTIFY_PREF: str = os.getenv("NOTIFY_PREF", "sms_first").strip().lower()
    OFFICE_HOURS: dict = field(default_factory=lambda: {
        "start": os.getenv("OFFICE_HOURS_START", "08:00"),
        "end": os.getenv("OFFICE_HOURS_END", "18:00"),
    })

    OLT_NOTIFY_MIN_AFFECTED: int = 1
    OLT_NOTIFY_COOLDOWN_S: int = 300
    OLT_NOTIFY_BYPASS_ON_ESCALATE: bool = True
    OLT_NOTIFY_MIN_CONFIDENCE: float = 0.0
    CLUSTER_NOTIFY_COOLDOWN_S: int = 900
    OFFLINE_GRACE_SECONDS: int = 60

    # Daily status (moved under /var/lib by default)
    DAILY_STATUS_PATH: str = os.getenv("DAILY_STATUS_PATH", str(STATE_DIR / "daily_status.json"))
    DAILY_STATUS_RECIPIENTS: list[str] = field(default_factory=lambda: _env_list("DAILY_STATUS_RECIPIENTS") or [
        "<EMAIL>", "<EMAIL>"
    ])

    # Ticketing
    CRM_TICKETS_PATH: str = os.getenv("CRM_TICKETS_PATH", "/ticketing/tickets")
    TICKETS_ASSIGNED_GROUP_ID: int | None = int(os.getenv("TICKETS_ASSIGNED_GROUP_ID", "0") or "0") or None
    TICKETS_ASSIGNED_USER_ID: int | None = int(os.getenv("TICKETS_ASSIGNED_USER_ID", "0") or "0") or None
    TICKETS_CLOSED_STATUS: int = int(os.getenv("TICKETS_CLOSED_STATUS", "3"))

    # Staff lists
    SUPPORT_EMAILS: list[str] = field(default_factory=lambda: _env_list("SUPPORT_EMAILS"))
    MODERATE_MAIL: str = os.getenv("MODERATE_MAIL", "<EMAIL>")
    URGENT_MAIL: str = os.getenv("URGENT_MAIL", "<EMAIL>")
    CRITICAL_MAIL: str = os.getenv("CRITICAL_MAIL", "<EMAIL>")
    SUPPORT_SMS: list[str] = field(default_factory=lambda: _env_list("SUPPORT_SMS") or ["+17576958080"])
    NOC_HD365_SMS: list[str] = field(default_factory=lambda: _env_list("NOC_HD365_SMS"))
    FIELD_SUPERVISOR_SMS: list[str] = field(default_factory=lambda: _env_list("FIELD_SUPERVISOR_SMS"))
    AFTER_HOURS_EMAILS: list[str] = field(default_factory=lambda: _env_list("AFTER_HOURS_EMAILS"))

    # Storage (default to /var/lib/outage-monitor on *nix; keep Windows dev sane)
    SMS_STORE_PATH: str = os.getenv(
        "SMS_STORE_PATH",
        r"c:\temp\sms_store.sqlite3" if os.name == "nt" else str(STATE_DIR / "sms_store.sqlite3")
    )
    TICKET_STORE_PATH: str = os.getenv("TICKET_STORE_PATH", str(STATE_DIR / "tickets.sqlite3"))
    PROBE_DB_PATH: str = os.getenv("PROBE_DB_PATH", str(STATE_DIR / "probe.db"))
    DATABASE_URL = "sqlite:////var/lib/outage-monitor/monitor.db"
    # retention knobs (tune as you like)
    RAW_SAMPLE_RETENTION_DAYS = 14
    AGG_RETENTION_DAYS = 120

    # Vitelity SMS
    VITELITY_USERNAME: str = os.getenv("VITELITY_USERNAME", "esho_api")
    VITELITY_FROM_NUMBER: str = os.getenv("VITELITY_FROM_NUMBER", "2525486200")
    VITELITY_SEND_API_URL: str = "https://smsout-api.vitelity.net/api.php"
    VITELITY_API_URL: str = "https://api.vitelity.net/api.php"
    VITELITY_PASSWORD: str | None = os.getenv("VITELITY_PASSWORD")
    SMS_RATE_PER_MIN: int = int(os.getenv("SMS_RATE_PER_MIN", "50"))

    # SNMP & mappings (unchanged from your current file)
    SNMP_COMMUNITY: str = os.getenv("SNMP_COMMUNITY", "SuperCow")

    OLT_PROBES: dict = field(default_factory=lambda: {
        "0a0b800d-81d2-4eb1-95ab-8fcf00dec7b6": {"name": "NC-CMD-WTN-UF-OLT-1", "olt_ip": "************",
                                                 'fs_ip': '************', 'fs_uplink_ifname': 'ethernet 1/19'},
        "faac8f84-fc00-4e36-b06a-59abcb215357": {"name": "NC-CMD-WTN-UF-OLT-2", "olt_ip": "************",
                                                 'fs_ip': '************', 'fs_uplink_ifname': 'ethernet 1/27'},
        "39e60cec-736d-48f9-b9c5-e4929b43c92a": {"name": "NC-CMD-CCP-UF-OLT-1", "olt_ip": "************",
                                                 'fs_ip': '************', 'fs_uplink_ifname': 'ethernet 1/26'},
        "3fbfcb3e-97bc-4218-93b6-3c7be73b61a8": {"name": "NC-CMD-CCP-UF-OLT-2", "olt_ip": "************",
                                                 'fs_ip': '************', 'fs_uplink_ifname': 'ethernet 1/27'},
        "fa1d0454-14a9-4c5d-aaa0-426671107b0c": {"name": "NC-CMD-OFF-UF-OLT-1", "olt_ip": "************",
                                                 'fs_ip': '************', 'fs_uplink_ifname': 'ethernet 1/26'},
        "83f4ffb6-a371-4410-b912-189127c07e4b": {"name": "NC-CMD-WTS-UF-OLT-1", "olt_ip": "************",
                                                 'fs_ip': '************', 'fs_uplink_ifname': 'ethernet 1/25'},
        "64c41c50-e947-4d66-84fa-cbd88594744c": {"name": "NC-CMD-WL-UF-OLT-1", "olt_ip": "***********",
                                                 'fs_ip': '***********', 'fs_uplink_ifname': 'ethernet 1/28'},
    })

    FS_NAMES: dict = field(default_factory=lambda: {
        "************": "NC-CMD-WTN",
        "************": "NC-CMD-CCP",
        "************": "NC-CMD-WTS",
        "************": "NC-CMD-OFF",
        "***********": "NC-CMD-WL",
    })

    FS_LINK_PROBES: list[dict] = field(default_factory=lambda: [
        {"olt_id": "link-ccp-25-wl", "name": "LINK CCP 25 → WL",
         "fs_ip": "************", "fs_ifname": "ethernet 1/25"},
        {"olt_id": "link-wl-25-wtn", "name": "LINK WL 25 → WTN",
         "fs_ip": "***********", "fs_ifname": "ethernet 1/25"},
        {"olt_id": "link-wl-26-ccp", "name": "LINK WL 26 → CCP",
         "fs_ip": "***********", "fs_ifname": "ethernet 1/26"},
        {"olt_id": "link-wtn-25-off", "name": "LINK WTN 25 → OFF",
         "fs_ip": "************", "fs_ifname": "ethernet 1/25"},
        {"olt_id": "link-wtn-26-wl", "name": "LINK WTN 26 → WL",
         "fs_ip": "************", "fs_ifname": "ethernet 1/26"},
        {"olt_id": "link-wtn-28-ec", "name": "LINK WTN 28 → EC FS5800",
         "fs_ip": "************", "fs_ifname": "ethernet 1/28"},
        {"olt_id": "link-off-25-wtn", "name": "LINK OFF 25 → WTN",
         "fs_ip": "************", "fs_ifname": "ethernet 1/25"},
        {"olt_id": "link-off-27-wts", "name": "LINK OFF 27 → WTS",
         "fs_ip": "************", "fs_ifname": "ethernet 1/27"},
        {"olt_id": "link-wts-27-off", "name": "LINK WTS 27 → OFF",
         "fs_ip": "************", "fs_ifname": "ethernet 1/27"},
    ])

    LOCATIONS: dict[str, dict] = field(default_factory=lambda: {
        "wtn": {"site": "b7cda07e-decc-40b0-b7c6-6897135a5645",
                "olts": ["0a0b800d-81d2-4eb1-95ab-8fcf00dec7b6", "faac8f84-fc00-4e36-b06a-59abcb215357"]},
        "ccp": {"site": "1a782c6d-657f-4a5b-8a46-c3260a114281",
                "olts": ["39e60cec-736d-48f9-b9c5-e4929b43c92a", "3fbfcb3e-97bc-4218-93b6-3c7be73b61a8"]},
        "wts": {"site": "6725279f-051f-4977-8a74-ac0f3322c21b",
                "olts": ["fa1d0454-14a9-4c5d-aaa0-426671107b0c"]},
        "off": {"site": "79740732-3b6b-48d3-8a18-7559da9bef4e",
                "olts": ["83f4ffb6-a371-4410-b912-189127c07e4b"]},
        "wl":  {"site": "6d1520a9-1da0-4cdf-b903-9a9942bb9d43",
                "olts": ["64c41c50-e947-4d66-84fa-cbd88594744c"]},
    })

# Instantiate
settings = Settings()

# Final sanity: refuse to write into releases
_assert_paths_sane([
    Path(settings.SMS_STORE_PATH),
    Path(settings.TICKET_STORE_PATH),
    Path(settings.PROBE_DB_PATH),
    Path(settings.DAILY_STATUS_PATH),
])
