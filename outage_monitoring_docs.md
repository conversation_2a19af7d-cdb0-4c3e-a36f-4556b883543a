# Outage Monitoring System Documentation

This document provides a complete overview of the outage monitoring system, including architecture, setup instructions, module descriptions, API reference, and rebuild guide.

---

## Table of Contents
1. [Overview](#overview)
2. [Architecture](#architecture)
3. [Setup Instructions](#setup-instructions)
4. [Module Descriptions](#module-descriptions)
5. [API Reference](#api-reference)
6. [Database Schema](#database-schema)
7. [Notification System](#notification-system)
8. [Rebuild Guide](#rebuild-guide)
9. [Developer Notes](#developer-notes)

---

## Overview
This system monitors FS3900 switches and ONUs for outages and degraded performance. It collects SNMP data, classifies severity, persists metrics, and sends notifications via email/SMS. It also exposes a read-only API for dashboards and external integrations.

---

## Architecture
- **Probe Layer**: SNMP polling and classification (`fs3900_probe.py`)
- **Storage Layer**: SQLite persistence and rollups (`storage.py`)
- **Notification Layer**: Email/SMS delivery and templates (`notify.py`)
- **Task Layer**: Periodic jobs and orchestration (`tasks.py`, `probe_and_notify.py`)
- **API Layer**: FastAPI read-only endpoints (`fastapi_app.py`)

---

## Setup Instructions
1. **Install Dependencies**:
   ```bash
   pip install fastapi uvicorn matplotlib pydantic requests
   ```

2. **Environment Variables**:
   - `NOC_DB_PATH`: Path to SQLite DB
   - `NOC_STATE_PATH`: Path to probe state JSON
   - `NOC_JOURNAL_PATH`: Path to outage journal
   - `NOC_API_KEY`: API key for authentication
   - `SMTP_HOST`, `SMTP_PORT`, `SMTP_USER`, `SMTP_PASSWORD`, `FROM_EMAIL`

3. **Initialize Database**:
   ```python
   from storage import init_probe_db
   init_probe_db("./device_probe.sqlite3")
   ```

4. **Run FastAPI Server**:
   ```bash
   uvicorn fastapi_app:app --reload
   ```

5. **Run Probe Tasks**:
   Use `tasks.py` with a scheduler (e.g. cron or Celery).

---

## Module Descriptions
### `fs3900_probe.py`
- SNMP polling logic
- DOM classification
- Interface status checks

### `storage.py`
- SQLite schema and connection
- Raw sample persistence
- 5m and 30m rollups
- DOM threshold cache

### `notify.py`
- Email and SMS delivery
- Message templates for customers and staff
- Daily digest formatting

### `tasks.py`
- Periodic rollups and retention
- Daily summary generation
- Notification dispatch

### `probe_and_notify.py`
- Outage detection and journaling
- Ticketing integration
- Notification orchestration

### `fastapi_app.py`
- Read-only API endpoints
- OLT page bundles
- Chart.js and PNG throughput charts
- Switch snapshots and sparklines

---

## API Reference
- `/v1/olt/{olt_id}/meta`
- `/v1/olt/{olt_id}/current_offline`
- `/v1/olt/{olt_id}/recent_outages`
- `/v1/samples/latest`
- `/v1/ui/chartjs/throughput`
- `/v1/switch/{fs_ip}/snapshot`
- `/v1/state/onus`

All endpoints require `x-api-key` header unless `NOC_API_KEY` is unset.

---

## Database Schema
- `samples_raw`: Raw SNMP samples
- `agg_5m`, `agg_30m`: Aggregated metrics
- `devices`: Metadata for OLTs and links
- `dom_thresholds`: Cached DOM thresholds

---

## Notification System
- SMS via Vitelity API
- Email via SMTP
- Templates for:
  - Customer outage/recovery
  - Staff outage/recovery
  - API health alerts
  - Daily digest

---

## Rebuild Guide
To rebuild this project from scratch:
1. Provide the purpose and goals (e.g. migrate to PostgreSQL, add real-time alerts).
2. Specify deployment environment (e.g. Docker, cloud provider).
3. Describe data sources (e.g. switch models, SNMP agents).
4. Define desired outputs (e.g. dashboards, CSV exports).
5. Share performance targets (e.g. support 1000+ endpoints).

Then I can regenerate the full codebase and documentation.

---

## Developer Notes
- SQLite uses WAL mode for concurrency
- SNMP polling is batched for performance
- DOM classification uses thresholds and severity ranks
- API is read-only and safe for dashboards
- Email/SMS logic is idempotent and rate-limited

---

Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
