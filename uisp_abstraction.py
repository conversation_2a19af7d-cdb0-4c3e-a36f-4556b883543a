from __future__ import annotations
import os, json, time
import requests
import logging
from typing import Dict, Any, List, Optional, Tuple
from urllib.parse import urljoin
from requests.exceptions import ConnectionError, Timeout, HTTPError

from config import Settings, settings

log = logging.getLogger(__name__)


class _NSMClient:
    """
    Client for the UISP Network (NSM) API, handling all device-related queries.
    """

    def __init__(self, base_url: str, token: str, debug: bool = False):
        # The NSM base URL ends in a path segment, so urljoin is the correct tool.
        self.base_url = base_url.rstrip('/')
        self.token = token
        self.debug = debug
        self.session = requests.Session()
        self.session.headers.update({
            "x-auth-token": self.token,
            "Content-Type": "application/json",
            "Accept": "application/json"
        })

    def _get(self, path: str, params: Optional[Dict[str, Any]] = None) -> Dict[str, Any] | List[Dict[str, Any]]:
        """
        Internal helper for GET requests with granular error handling.

        Raises:
            requests.exceptions.HTTPError: For 4xx/5xx HTTP errors.
            requests.exceptions.ConnectionError: For network-related issues.
            requests.exceptions.Timeout: If the request times out.
        """
        url = urljoin(self.base_url + '/', path)
        try:
            log.debug(f"GET {url} with params {params}")
            resp = self.session.get(url, params=params, timeout=10)
            resp.raise_for_status()
            return resp.json()
        except HTTPError as e:
            log.error(f"HTTP error on GET {url}: {e}")
            raise
        except (ConnectionError, Timeout) as e:
            log.error(f"Network error on GET {url}: {e}")
            raise
        except Exception as e:
            log.error(f"An unexpected error occurred on GET {url}: {e}")
            raise

    def list_onus_by_site(self, site_id: str) -> List[Dict[str, Any]]:
        """
        Fetches all endpoints (ONUs) for a given site ID and extracts relevant status data.

        Args:
            site_id: The ID of the UISP site (e.g., from config.py).

        Returns:
            A list of dictionaries, where each dictionary represents an ONU with its ID,
            name, status, and suspended flag.
        """
        path = f"sites/{site_id}"
        site_data = self._get(path)

        if not isinstance(site_data, dict) or "description" not in site_data:
            log.error(f"Unexpected API response for site {site_id}")
            return []

        endpoints = site_data["description"].get("endpoints", [])

        extracted_data = []
        for endpoint in endpoints:
            if all(k in endpoint for k in ["id", "name", "status", "suspended"]):
                extracted_data.append({
                    "id": endpoint["id"],
                    "name": endpoint["name"],
                    "status": endpoint["status"],
                    "suspended": endpoint["suspended"]
                })
        return extracted_data


class _CRMClient:
    """
    Client for the UISP CRM API, handling all customer and ticket-related operations.
    """

    def __init__(self, base_url: str, token: str, debug: bool = False):
        # The CRM base URL is different, so we need to use urljoin to construct paths.
        self.base_url = base_url.rstrip('/')
        self.token = token
        self.debug = debug
        self.session = requests.Session()
        self.session.headers.update({
            "x-auth-token": self.token,
            "Content-Type": "application/json",
            "Accept": "application/json"
        })

    def _get(self, path: str, params: Optional[Dict[str, Any]] = None) -> Dict[str, Any] | List[Dict[str, Any]]:
        """
        Internal helper for GET requests with granular error handling.
        """
        url = urljoin(self.base_url + '/', path)
        try:
            log.debug(f"CRM GET {url} with params {params}")
            resp = self.session.get(url, params=params, timeout=10)
            resp.raise_for_status()
            return resp.json()
        except HTTPError as e:
            log.error(f"CRM HTTP error on GET {url}: {e}")
            raise
        except (ConnectionError, Timeout) as e:
            log.error(f"CRM Network error on GET {url}: {e}")
            raise
        except Exception as e:
            log.error(f"An unexpected error occurred on GET {url}: {e}")
            raise

    def find_client_by_name(self, name: str) -> Optional[int]:
        """
        Searches for a client by name using the CRM API's search endpoint.

        Args:
            name: The name of the client to search for.

        Returns:
            The client ID as an integer, or None if not found.
        """
        try:
            # The UISP CRM API uses a search endpoint for clients
            clients = self._get("clients", params={"name": name, "strict": True})
            if clients and isinstance(clients, list) and clients[0]:
                return int(clients[0].get("id"))
        except Exception as e:
            log.error(f"Failed to find client by name '{name}': {e}")
            return None
        return None

    def client_contact_channels(self, client_id: int) -> dict[str, Any]:
        """
        Fetches a client's contact channels from the CRM.

        Args:
            client_id: The ID of the client.

        Returns:
            A dictionary containing the client's contact channels.
        """
        return self._get(f"clients/{client_id}/contact-channels")

    def create_ticket(self, client_id: int, subject: str, note: Optional[str] = None, dry_run: bool = True) -> Optional[
        int]:
        """
        Creates a new support ticket in the CRM.
        """
        log.info(f"DRY RUN: create_ticket for client {client_id} with subject '{subject}'")
        return None

    def update_ticket(self, ticket_id: int, note: Optional[str] = None, status: Optional[str] = None,
                      dry_run: bool = True) -> bool:
        """
        Updates an existing support ticket.
        """
        log.info(f"DRY RUN: update_ticket {ticket_id} with status '{status}' and note '{note}'")
        return True


from dataclasses import dataclass


@dataclass(frozen=True)
class UISPService:
    """
    A single, high-level service class to interact with both NSM and CRM APIs.
    """
    nsm: _NSMClient
    crm: _CRMClient

    @classmethod
    def from_config(cls, settings: Settings, debug: bool = False) -> UISPService:
        """Convenience constructor to build service clients from settings."""
        nsm_client = _NSMClient(
            base_url=settings.NSM_BASE_URL,
            token=settings.NSM_TOKEN,
            debug=debug
        )
        crm_client = _CRMClient(
            base_url=settings.CRM_BASE_URL,
            token=settings.CRM_TOKEN,
            debug=debug
        )
        return cls(nsm=nsm_client, crm=crm_client)

    def get_user_messaging_settings(self, client_id: int) -> dict:
        """
        Retrieves a client's messaging opt-in/opt-out settings from CRM.

        Args:
            client_id: The ID of the client.

        Returns:
            A dictionary with 'sms_notify' and 'email_notify' boolean flags.
        """
        contact_channels = self.crm.client_contact_channels(client_id)

        email_notify = any(
            channel.get("emailNotification")
            for channel in contact_channels.get("emails", [])
            if channel.get("email")
        )

        sms_notify = any(
            channel.get("smsNotification")
            for channel in contact_channels.get("phones", [])
            if channel.get("phone")
        )

        return {
            "sms_notify": sms_notify,
            "email_notify": email_notify
        }
