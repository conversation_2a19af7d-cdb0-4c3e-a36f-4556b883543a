from __future__ import annotations
from celery import Celery
from celery.schedules import crontab
from config import settings

app = Celery(
    "outage_monitor",
    broker=settings.CELERY_BROKER_URL,
    backend=settings.CELERY_RESULT_BACKEND,
)

app.conf.update(
    timezone=getattr(settings, "CELERY_TIMEZONE", settings.TIMEZONE),
    enable_utc=False,
    task_serializer=settings.CELERY_TASK_SERIALIZER,
    result_serializer=settings.CELERY_RESULT_SERIALIZER,
    accept_content=settings.CELERY_ACCEPT_CONTENT,
    worker_prefetch_multiplier=1,     # no over-buffering for short polls
    task_acks_late=False,
    task_time_limit=60,
    task_soft_time_limit=55,
    # Make sure the worker imports the task module
    imports=("periodic",),
    beat_schedule={
        "poll-nsm": {
            "task": "periodic.poll_nsm",
            "schedule": int(getattr(settings, "NSM_POLL_INTERVAL_SECS", 30)),
            "options": {"queue": "poll", "expires": int(getattr(settings, "NSM_POLL_INTERVAL_SECS", 30))},
        },
        "poll-fs3900": {
            "task": "periodic.poll_fs3900",
            "schedule": int(getattr(settings, "FS_POLL_INTERVAL_SECS", 90)),
            "options": {"queue": "poll", "expires": int(getattr(settings, "FS_POLL_INTERVAL_SECS", 90))},
        },
        "rollup-5m": {
            "task": "db.rollup_5m",
            "schedule": 60.0,
        },
        # NEW: aggregate the last closed hour every 5 minutes
        "rollup-60m": {
            "task": "db.rollup_60m",
            "schedule": 300.0,
        },
        # NEW: daily retention housekeeping
        "retention-daily": {
            "task": "db.retention",
            "schedule": crontab(minute=12, hour=3),
        },
    },
)
