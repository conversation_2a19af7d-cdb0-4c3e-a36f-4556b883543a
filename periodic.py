# periodic.py — periodic tasks that call your existing logic
from __future__ import annotations
import logging, os, sys
from pathlib import Path
from contextlib import contextmanager

from celery_app import app
from config import settings

# Ensure release dir is importable (Celery workers can have a skinny sys.path)
ROOT = Path(__file__).resolve().parent
if str(ROOT) not in sys.path:
    sys.path.insert(0, str(ROOT))

log = logging.getLogger(__name__)
RUN_DIR = Path(os.getenv("RUN_DIR", "/run/outage-monitor"))
RUN_DIR.mkdir(parents=True, exist_ok=True)

def _import_any(*names):
    """Try module names in order; return the first that imports."""
    for name in names:
        try:
            return __import__(name, fromlist=["*"])
        except ModuleNotFoundError:
            continue
    raise ModuleNotFoundError(f"None of {names!r} found on sys.path={sys.path}")

@contextmanager
def non_overlapping(name: str):
    """Best-effort single-flight guard via flock."""
    import fcntl
    lock_path = RUN_DIR / f"{name}.lock"
    with open(lock_path, "w") as fh:
        try:
            fcntl.flock(fh.fileno(), fcntl.LOCK_EX | fcntl.LOCK_NB)
            yield
        except BlockingIOError:
            log.warning("Skip %s: previous run still active", name)

@app.task(name="periodic.poll_nsm")
def poll_nsm() -> None:
    with non_overlapping("poll_nsm"):
        try:
            run_uisp_lookup = _import_any("tasks", "outage_monitor.tasks").run_uisp_lookup
        except Exception as e:
            log.exception("Unable to import run_uisp_lookup(): %s", e)
            return
        try:
            run_uisp_lookup()
        except Exception as e:
            log.exception("NSM poll failed: %s", e)

@app.task(name="periodic.poll_fs3900")
def poll_fs3900() -> None:
    with non_overlapping("poll_fs3900"):
        sleep_s = float(getattr(settings, "FS_TRAFFIC_SLEEP_S", 1.5))

        try:
            run_fs3900_scan = _import_any("tasks", "outage_monitor.tasks").run_fs3900_scan
            return run_fs3900_scan(sleep_s=sleep_s)
        except Exception as e:
            log.warning("tasks.run_fs3900_scan not available/failed (%s); using direct manager path", e)

        try:
            FS3900 = _import_any("FS3900_inspector", "fs3900_inspector")
            FS3900Manager = FS3900.FS3900Manager
        except Exception as e:
            log.exception("FS3900 module unavailable: %s", e)
            return

        links = getattr(settings, "FS_LINK_PROBES", []) or []
        if not links:
            log.warning("No FS_LINK_PROBES configured; nothing to do.")
            return

        # Map olt_id -> fs_ip and compute the exact ports we want to sample
        fs_by_olt = {}
        wanted_ports = set()
        import re
        _IFNAME_PORT_RE = re.compile(r"/\s*(\d+)\s*$")

        for link in links:
            olt_id = str(link.get("olt_id") or link.get("fs_ip") or "")
            fs_ip  = link.get("fs_ip")
            ifname = str(link.get("fs_ifname") or "")
            m = _IFNAME_PORT_RE.search(ifname)
            if olt_id and fs_ip:
                fs_by_olt[olt_id] = fs_ip
            if m:
                wanted_ports.add(int(m.group(1)))

        mgr = FS3900Manager(
            community=getattr(settings, "SNMP_COMMUNITY", "public"),
            timeout_s=float(getattr(settings, "SNMP_TIMEOUT_S", 0.6)),
            retries=int(getattr(settings, "SNMP_RETRIES", 1)),
            max_workers=int(getattr(settings, "FS_MAX_WORKERS", 6)),
            debug=False,
        )
        mgr.fs_by_olt = fs_by_olt  # override mapping
        if wanted_ports:
            mgr.ports = tuple(sorted(wanted_ports))  # <-- only sample what you configured

        snapshots = mgr.snapshot_all(sample_traffic=True, traffic_sleep_s=sleep_s)

        # Helper to parse "ethernet 1/25" -> 25
        import re
        _IFNAME_PORT_RE = re.compile(r"/\s*(\d+)\s*$")
        def _port_from_ifname(ifname: str):
            m = _IFNAME_PORT_RE.search(str(ifname))
            return int(m.group(1)) if m else None

        # Build quick (olt_id, port) -> (in_bps, out_bps)
        metrics = {}
        for olt_id, snap in (snapshots or {}).items():
            for p in snap.get("ports") or []:
                if isinstance(p, dict):
                    port_no = int(p.get("port", -1))
                    t = p.get("traffic") or {}
                    in_bps, out_bps = t.get("in_bps"), t.get("out_bps")
                else:
                    port_no = int(getattr(p, "port", -1))
                    t = getattr(p, "traffic", None)
                    in_bps = getattr(t, "in_bps", None) if t else None
                    out_bps = getattr(t, "out_bps", None) if t else None
                if port_no >= 0:
                    metrics[(olt_id, port_no)] = (in_bps, out_bps)

        # Emit the per-link lines you expect
        for link in links:
            olt_id = str(link.get("olt_id") or link.get("fs_ip"))
            fs_ip  = link.get("fs_ip")
            ifname = str(link.get("fs_ifname"))
            port   = _port_from_ifname(ifname)
            if not (olt_id and fs_ip and port is not None):
                continue
            in_bps, out_bps = metrics.get((olt_id, port), (None, None))
            if in_bps is None or out_bps is None:
                log.warning("FS %s %s: no traffic sample", fs_ip, ifname)
            else:
                log.info("FS %s %s: in=%.0f bps out=%.0f bps (sleep=%.3fs)", fs_ip, ifname, in_bps, out_bps, sleep_s)

        log.info("FS scan: done")

