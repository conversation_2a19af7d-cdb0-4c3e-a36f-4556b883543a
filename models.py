from __future__ import annotations
from datetime import datetime
from typing import Optional
from sqlalchemy.orm import DeclarativeBase, Mapped, mapped_column
from sqlalchemy import String, Integer, Float, DateTime, Index, PrimaryKeyConstraint

class Base(DeclarativeBase):
    pass

# --- Raw per-port samples (traffic + DOM) -------------------------
class FsMetricSample(Base):
    __tablename__ = "fs_metric_sample"
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    ts: Mapped[datetime] = mapped_column(DateTime, index=True)
    fs_ip: Mapped[str] = mapped_column(String(64), index=True)
    fs_label: Mapped[Optional[str]] = mapped_column(String(128), nullable=True)
    port: Mapped[int] = mapped_column(Integer, index=True)
    ifname: Mapped[Optional[str]] = mapped_column(String(64), nullable=True)

    in_bps: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    out_bps: Mapped[Optional[float]] = mapped_column(Float, nullable=True)

    temp_c:  Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    vcc_v:   Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    bias_ma: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    tx_dbm:  Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    rx_dbm:  Mapped[Optional[float]] = mapped_column(Float, nullable=True)

    # Optional rollup keys: link/olt context when available
    key: Mapped[Optional[str]] = mapped_column(String(128), nullable=True)   # e.g. OLT id or link id
    kind: Mapped[Optional[str]] = mapped_column(String(32), nullable=True)   # "link" | "olt_port" | "plain"

    __table_args__ = (
        Index("ix_fs_sample_host_port_ts", "fs_ip", "port", "ts"),
    )

# --- Aggregates: 5m + 60m ----------------------------------------
class FsMetricAgg5m(Base):
    __tablename__ = "fs_metric_agg_5m"
    bucket_start: Mapped[datetime] = mapped_column(DateTime)
    fs_ip: Mapped[str] = mapped_column(String(64))
    port: Mapped[int] = mapped_column(Integer)

    avg_in_bps:  Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    p95_in_bps:  Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    max_in_bps:  Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    avg_out_bps: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    p95_out_bps: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    max_out_bps: Mapped[Optional[float]] = mapped_column(Float, nullable=True)

    avg_temp_c:  Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    min_rx_dbm:  Mapped[Optional[float]] = mapped_column(Float, nullable=True)

    n: Mapped[int] = mapped_column(Integer, default=0)

    __table_args__ = (
        PrimaryKeyConstraint("bucket_start", "fs_ip", "port", name="pk_agg5m"),
    )

class FsMetricAgg60m(Base):
    __tablename__ = "fs_metric_agg_60m"
    bucket_start: Mapped[datetime] = mapped_column(DateTime)
    fs_ip: Mapped[str] = mapped_column(String(64))
    port: Mapped[int] = mapped_column(Integer)

    avg_in_bps:  Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    p95_in_bps:  Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    max_in_bps:  Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    avg_out_bps: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    p95_out_bps: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    max_out_bps: Mapped[Optional[float]] = mapped_column(Float, nullable=True)

    avg_temp_c:  Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    min_rx_dbm:  Mapped[Optional[float]] = mapped_column(Float, nullable=True)

    n: Mapped[int] = mapped_column(Integer, default=0)

    __table_args__ = (
        PrimaryKeyConstraint("bucket_start", "fs_ip", "port", name="pk_agg60m"),
    )

# --- ONU tracking -------------------------------------------------
class OnuStatusCurrent(Base):
    __tablename__ = "onu_status_current"
    olt_id: Mapped[str] = mapped_column(String(64), primary_key=True)
    onu_id: Mapped[str] = mapped_column(String(128), primary_key=True)  # use stable UISP id
    name: Mapped[Optional[str]] = mapped_column(String(256), nullable=True)
    address: Mapped[Optional[str]] = mapped_column(String(512), nullable=True)
    state: Mapped[str] = mapped_column(String(16))  # "online" | "offline" | "suspended"
    since: Mapped[datetime] = mapped_column(DateTime)  # when this state started

class OnuOutage(Base):
    __tablename__ = "onu_outage"
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    olt_id: Mapped[str] = mapped_column(String(64), index=True)
    onu_id: Mapped[str] = mapped_column(String(128), index=True)
    name: Mapped[Optional[str]] = mapped_column(String(256), nullable=True)
    address: Mapped[Optional[str]] = mapped_column(String(512), nullable=True)
    start_ts: Mapped[datetime] = mapped_column(DateTime, index=True)
    end_ts: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True, index=True)  # null until recovered
