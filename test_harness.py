from __future__ import annotations
import logging
import os
import sys

# Add the parent directory to the path so we can import modules
# This is a common practice for runnable scripts in nested directories
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from uisp_abstraction import UISPService
from tasks import run_probe_on_single_switch, run_uisp_lookup
from config import settings

# Configure logging for debug mode
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
log = logging.getLogger(__name__)


def run_test_harness():
    """
    Executes a comprehensive test of the UISP and FS3900 probes in debug mode,
    and simulates a CRM ticket creation for a specific test user.
    """
    log.info("Starting network probe test harness...")

    # Initialize the UISP service in debug mode as requested
    svc = UISPService.from_config(settings=settings, debug=True)

    # Run the existing UISP and FS3900 lookup functions
    log.info("\n--- Running UISP Lookup Test ---")
    run_uisp_lookup()

    log.info("\n--- Running FS3900 Probe Test ---")
    run_probe_on_single_switch()

    # --- CRM Client Test ---
    log.info("\n--- Simulating CRM Ticket Creation ---")
    test_client_id = 13
    test_email = "<EMAIL>"
    test_phone = "7576958080"

    # NOTE: The create_ticket and update_ticket methods in uisp_abstraction.py are
    # currently set to dry_run=True, so this will only log the action.
    try:
        log.info(f"Attempting to find client ID for '{test_email}'")
        client_id_from_search = svc.crm.find_client_by_name("rvgeijn")
        log.info(f"Client ID found via search: {client_id_from_search}")

        ticket_id = svc.crm.create_ticket(
            client_id=test_client_id,
            subject="Test ticket from automated harness",
            note=f"This is a test ticket created by the probe test harness. Test user details: Email={test_email}, Phone={test_phone}"
        )

        if ticket_id:
            log.info(f"Test ticket created with ID: {ticket_id}")
            svc.crm.update_ticket(
                ticket_id=ticket_id,
                status="resolved",
                note="Test ticket has been resolved by the automated harness."
            )
        else:
            log.info("Ticket creation was a dry run. No ticket ID returned.")

    except Exception as e:
        log.error(f"CRM client test failed: {e}")

    log.info("\nTest harness finished.")


if __name__ == "__main__":
    run_test_harness()
