# Outage Monitor — Installation & Operations

This guide describes a production-grade setup on Debian/Ubuntu Linux with:
- Code under versioned **release** directories: `/opt/outage-monitor/releases/<timestamp>`
- Stable symlink to the active release: `/opt/outage-monitor/current`
- Config and secrets in `/etc/outage-monitor/`
- State in `/var/lib/outage-monitor/`
- Logs in `/var/log/outage-monitor/`
- systemd unit managing the service

> **Compatibility:** Application code imports settings via `from config import settings`. That contract stays the same.

---

## 1) Server Requirements

```bash
sudo apt-get update
sudo apt-get install -y git python3 python3-venv build-essential \
  python3-dev libffi-dev libssl-dev pkg-config
# Optional per your deps:
# sudo apt-get install -y libpq-dev libxml2-dev libxslt1-dev libcairo2-dev libpango1.0-dev
